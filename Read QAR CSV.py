# coding=utf-8
# coding = gbk
import tkinter
import pandas as pd
import datetime
import time
import os
import tkinter as tk
from pandas import *
from datetime import *
from tkinter import *
import xlsxwriter


nowyear = datetime.now().year
nowmonth = datetime.now().month
Dateend = pd.Timestamp(datetime(nowyear, nowmonth, 1, 0, 0, 0))  # (yyyy,m,d,hh,mm,ss)
if nowmonth==1:
    Datestart = pd.Timestamp(datetime(nowyear-1, 12, 1, 0, 0, 0))  # (yyyy,m,d,hh,mm,ss)
else:
    Datestart = pd.Timestamp(datetime(nowyear, nowmonth-1, 1, 0, 0, 0))
start_time=datetime.now()


#获取文件路径及文件夹列表
currentpath=os.getcwd()
file_list=os.listdir(currentpath)


def read_data(x,y):
        
        l=0      #用来标记CSV文件处理次数
        for file in file_list:
            fileformat = os.path.splitext(file)[1]
            filename=os.path.splitext(file)[0]+os.path.splitext(file)[1]               #获取文件名

            if (filename == "Landing Score.csv"):
                print("抓取到Landing Score文件请稍后...")
                outfilename = currentpath + "\\"+os.path.splitext(file)[0]
                fullfilename= currentpath + "\\"+os.path.splitext(file)[0]+".csv"
                l_columns=['飞机号','起飞机场','着陆机场','航班号','起飞日期','起飞时间','落地时间','着陆得分','着陆风修正','着陆最终得分','得分1','得分2','得分3','得分4','得分5','得分6','得分7','得分8','得分9','得分10','得分11','得分12','得分13','得分14','得分15','得分16','得分17','得分18','得分19','得分20','得分21','进跑道高度','拉开是高度','接近拉平高度','油门动作下降率','反推动作时间','着陆姿态','着陆中最大姿态变化率','	擦机尾风险','接地后姿态增加','放前轮时间','接地坡度','着陆期间最大坡度','着陆期间坡度习惯','着陆航向偏离','最大着陆载荷','5英尺下降率','接地点距离','下滑点稳定性','着陆下沉一致性','着陆姿态一致性','大行程晃杆','着陆PF','进近PF','接地PF','进近模式','进近跑道','复飞机场','复飞高度','着陆风等级','着陆最大侧风','着陆双操纵时间','接管','航段总时间','夜航时间','着陆夜航','时间错误','着陆形态','操作干预','自动驾驶断开高度','着陆设备使用','稳定进近高度','高进近']
                print("数据读取中......")
                Lscore=pd.read_csv(fullfilename,sep=';',header=None,names=l_columns,skiprows=123838)   #跳过前面123838行因为之前格式不一致
                Lscore['起飞时间全']=Lscore["起飞日期"]+" "+Lscore["起飞时间"]
                Lscore["起飞时间全"]=[datetime.strptime(i ,'%Y/%m/%d %H:%M:%S') for i in Lscore["起飞时间全"]]
                Lselect=Lscore.loc[(Lscore["起飞时间全"]>=x)&(Lscore["起飞时间全"]<y)]
                print("数据写入中......")
                Lselect.to_excel(outfilename+'.xlsx',header=True, index=None)   
                print("导出数据完成" )
                l=l+1  

            #ARJ21Base report   
            if (filename == "ARJ21_B01_Base_Report.csv"):
                print("抓取到ARJ21_Base_Report文件请稍后...")
                outfilename = currentpath + "\\"+os.path.splitext(file)[0]
                fullfilename= currentpath + "\\"+os.path.splitext(file)[0]+".csv"
                l_columns=['飞机号','起飞机场','着陆机场','航班号','起飞日期','起飞时间','落地时间','最大飞行高度','最大空速','最大马赫数','最大空中载荷','着陆载荷','接地点距离','着陆减速到40节跑道距离','着陆航向偏离','离地俯仰姿态','接地俯仰姿态','滑行最大推力','地面滑行最大地速','地面转弯最大地速','V1','VR','V2','VREF','抬轮速度','离地速度','接地速度','AP接通高度','ATS接通高度','收起落架迟高度','AP断开高度','放起落架高度','起飞15度前最大俯仰率','起飞15度后最大俯仰率','起飞全重','着陆全重','起飞10-20英尺最大俯仰率','最大选择马赫数','最大选择高度','空中小时耗油','0-3000英尺耗油','平飞耗油','3000-0英尺耗油','首次接地载荷','扰流板放出后最大载荷','VAPP','收油门高度','进近模式','着陆风等级','稳定进近高度','是否高进近','下降率1','下降率2','下降率3','下降率4','下降率5','下降率6']
                print("数据读取中......")
                
                Lbase=pd.read_csv(fullfilename,sep=';',header=None,names=l_columns,encoding="utf-8")   #跳过前面44150行因为之前格式不一致skiprows=44150,
               
                Lbase['起飞时间全']=Lbase["起飞日期"]+" "+Lbase["起飞时间"]
                Lbase["起飞时间全"]=[datetime.strptime(i ,'%Y/%m/%d %H:%M:%S') for i in Lbase["起飞时间全"]]
                Lselect=Lbase.loc[(Lbase["起飞时间全"]>=x)&(Lbase["起飞时间全"]<y)]
                print("数据写入中......")
                Lselect.to_excel(outfilename+'.xlsx',engine='xlsxwriter',index=None)
                print("导出数据完成")
        
        
        
        if l==0:
            print("请导入CSV文件")






def get_time():
    Datestart = WDatestart.get()
    Dateend = WDateend.get()
    print(Datestart)
    print(Dateend)
    read_data(Datestart,Dateend)
    print(datetime.now()-start_time)
    Bd.quit()



#显示窗口
Bd = tkinter.Tk()
Bd.title("请输入时间范围")

Label(Bd, text="起始日期").grid(row=0, column=0)
WDatestart = tk.Entry(Bd)
WDatestart.insert(0, Datestart)
WDatestart.grid(row=0, column=1)

Label(Bd, text="结束日期").grid(row=1, column=0)
WDateend = tk.Entry(Bd)
WDateend.insert(0, Dateend)
WDateend.grid(row=1, column=1)

button1 = tk.Button(Bd, text="确认时间", command=get_time)
button1.grid(row=2, column=1)
Bd.mainloop()







    
